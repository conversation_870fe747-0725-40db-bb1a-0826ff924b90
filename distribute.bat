@echo off
echo Building WARNO Mod Maker Redistributable Package...
echo.

rem Step 1: Clean previous builds
echo Cleaning previous builds...
if exist build rmdir /s /q build
if exist dist rmdir /s /q dist
if exist temp_input rmdir /s /q temp_input
if exist WARNO-Mod-Maker rmdir /s /q WARNO-Mod-Maker
if exist WarnoModMaker.jar del WarnoModMaker.jar
if exist WARNO-Mod-Maker.zip del WARNO-Mod-Maker.zip

rem Step 2: Build the fat JAR
echo.
echo Building fat JAR...
call build.bat

if %ERRORLEVEL% neq 0 (
    echo Build failed!
    exit /b %ERRORLEVEL%
)

rem Step 3: Rename fat JAR to expected name
echo.
echo Renaming fat JAR for distribution...
if exist WarnoModMaker.jar del WarnoModMaker.jar
rename WarnoModMaker-fat.jar WarnoModMaker.jar

rem Step 4: Create redistributable package with jpackage using Java 11
echo.
echo Creating redistributable package with jpackage...

rem Use the current Java that successfully compiled the code
where java >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo Error: No Java installation found in PATH
    exit /b 1
)

rem Get Java home from java.exe location
for /f "tokens=*" %%i in ('where java') do (
    set "JAVA_EXE=%%i"
    goto :found_java
)
:found_java

rem Extract JAVA_HOME from the java.exe path
for %%i in ("%JAVA_EXE%") do set "JAVA_HOME=%%~dpi.."
set "JAVA_HOME=%JAVA_HOME:\bin\..=%"

rem Verify jpackage exists
if not exist "%JAVA_HOME%\bin\jpackage.exe" (
    echo Error: jpackage not found in %JAVA_HOME%\bin
    echo This Java installation may not support jpackage
    echo Trying alternative Java installations...

    rem Try common Java locations
    if exist "C:\Program Files\Java\jdk-11\bin\jpackage.exe" (
        set "JAVA_HOME=C:\Program Files\Java\jdk-11"
        goto :java_found
    )
    if exist "C:\Program Files\Eclipse Adoptium\jdk-11\bin\jpackage.exe" (
        set "JAVA_HOME=C:\Program Files\Eclipse Adoptium\jdk-11"
        goto :java_found
    )
    if exist "%USERPROFILE%\.jdks\openjdk-24.0.1\bin\jpackage.exe" (
        set "JAVA_HOME=%USERPROFILE%\.jdks\openjdk-24.0.1"
        goto :java_found
    )

    echo Error: No Java installation with jpackage support found
    exit /b 1
)
:java_found

echo Using Java from: %JAVA_HOME%

rem Create a clean input directory with only the JAR
if not exist temp_input mkdir temp_input
copy WarnoModMaker.jar temp_input\

"%JAVA_HOME%\bin\jpackage" ^
    --input temp_input ^
    --name "WARNO-Mod-Maker" ^
    --main-jar WarnoModMaker.jar ^
    --main-class com.warnomodmaker.WarnoModMaker ^
    --type app-image ^
    --dest . ^
    --app-version 1.0 ^
    --vendor "WARNO Mod Maker" ^
    --description "WARNO Mod Maker - NDF File Editor for WARNO Game Modifications" ^
    --copyright "2025" ^
    --java-options "-Xmx2g"

if %ERRORLEVEL% neq 0 (
    echo jpackage failed!
    rmdir /s /q temp_input
    exit /b %ERRORLEVEL%
)

rem Clean up temporary input directory
rmdir /s /q temp_input

rem Step 5: Clean up any unwanted directories in the package
echo.
echo Cleaning package structure...
if exist WARNO-Mod-Maker\resources rmdir /s /q WARNO-Mod-Maker\resources

rem Step 6: Create ZIP distribution
echo.
echo Creating ZIP distribution...
powershell -Command "Compress-Archive -Path 'WARNO-Mod-Maker' -DestinationPath 'WARNO-Mod-Maker.zip' -Force"

if %ERRORLEVEL% neq 0 (
    echo ZIP creation failed!
    exit /b %ERRORLEVEL%
)

echo.
echo ========================================
echo Distribution package created successfully!
echo ========================================
echo.
echo Files created:
echo   - WARNO-Mod-Maker\          (Standalone application directory)
echo   - WARNO-Mod-Maker.exe       (Main executable)
echo   - WARNO-Mod-Maker.zip       (Complete distribution package)
echo.
echo To distribute:
echo   1. Share the WARNO-Mod-Maker.zip file
echo   2. Users extract and run WARNO-Mod-Maker.exe
echo   3. No Java installation required on user machines
echo.
echo Distribution complete!
