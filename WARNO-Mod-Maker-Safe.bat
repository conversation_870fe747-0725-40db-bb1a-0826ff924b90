@echo off
echo WARNO Mod Maker - Safe Launcher
echo ================================
echo.

rem Check if the main executable exists
if not exist "WARNO-Mod-Maker.exe" (
    echo Error: WARNO-Mod-Maker.exe not found in current directory
    echo Please make sure you extracted the full ZIP file
    pause
    exit /b 1
)

rem Try to run with different memory settings if the default fails
echo Attempting to start WARNO Mod Maker...
echo.

rem First try: Normal startup
echo Trying normal startup...
start "" "WARNO-Mod-Maker.exe"
timeout /t 5 /nobreak >nul

rem Check if process is running
tasklist /fi "imagename eq WARNO-Mod-Maker.exe" 2>nul | find /i "WARNO-Mod-Maker.exe" >nul
if %ERRORLEVEL% equ 0 (
    echo Application started successfully!
    echo.
    echo If you continue to have issues, try:
    echo 1. Restart your computer
    echo 2. Close other memory-intensive applications
    echo 3. Run as Administrator
    pause
    exit /b 0
)

echo Normal startup failed. This might be due to:
echo - Insufficient memory (need at least 2GB free RAM)
echo - Missing Visual C++ Redistributables
echo - Antivirus blocking the application
echo - Corrupted installation
echo.
echo Troubleshooting steps:
echo 1. Close other applications to free up memory
echo 2. Run as Administrator (right-click this file)
echo 3. Add exception to antivirus software
echo 4. Re-extract the ZIP file to a new location
echo 5. Restart your computer and try again
echo.
echo If problems persist, the application may not be compatible
echo with your system configuration.
echo.
pause
